<?php

require_once 'functions/utilities.php';
require_once 'functions/brand-settings-pages.php';
require_once 'functions/clean-menu.php';
require_once 'functions/topbar-menu.php';
require_once 'functions/release-note.php';
require_once 'functions/duplicate-post.php';
require_once 'functions/disable-emoji.php';
require_once 'functions/tinymce.php';
require_once 'functions/upload-files.php';
require_once 'functions/quiz-questions-count.php';
require_once 'functions/matchup.php';
require_once 'functions/help-boxes.php';
require_once 'functions/timezone-helper.php';
require_once 'functions/gutenberg-blocks.php';
require_once 'functions/acf-block-validation.php';
require_once 'functions/acf-fields.php';
require_once 'functions/acf-validation.php';
require_once 'functions/admin-columns.php';
require_once 'functions/quick-edit-fields.php';
// require_once 'functions/auto-purge-cache.php';

// Amend user role capabilities
function px_user_caps() {
    // Contributor
    $contributor = get_role('contributor');
    $contributor->add_cap('edit_pages');
    $contributor->remove_cap('publish_posts');

    // Editor
    $editor = get_role('editor');
    $editor->add_cap('publish_posts');
    $editor->add_cap('view_site_health_checks');
    $editor->add_cap('wpseo_bulk_edit');
    $editor->add_cap('wpseo_edit_advanced_metadata');
    $editor->add_cap('wpseo_manage_options');
    $editor->add_cap('wpseo_manage_redirects');
}
add_action('admin_init', 'px_user_caps');

// Admin area needed CSS/JS resources
function admin_resources() {
    global $post, $pagenow, $current_user;
    wp_enqueue_script('admin-script', get_template_directory_uri() . '/dist/admin.min.js', [], filemtime(get_template_directory() . '/dist/admin.min.js'), true);
    wp_enqueue_style('admin-style', get_stylesheet_directory_uri() . '/dist/admin.min.css', [], filemtime(get_template_directory() . '/dist/admin.min.css'));

    if (isFeatureActive('api/sportsbook')) {
        wp_localize_script('admin-script', 'matches_json', [json_encode(getMatches())]);
    }
    wp_localize_script('admin-script', 'icons', getIcons());

    $jackpotProviders = [];
    foreach (JACKPOT_PROVIDERS as $providerId => $provider) {
        if (isFeatureActive('api/jackpots/' . $providerId)) {
            $jackpotProviders[$providerId] = $provider;
            foreach ($provider['jackpots'] as $jackpotID => $jackpotName) {
                if(!function_exists('get_field')) continue;
                $jackpotNameTranslated = get_field('jackpot_games_jackpots_label_' . $jackpotID, 'option');
                $jackpotProviders[$providerId]['jackpots'][$jackpotID] = $jackpotNameTranslated ?: $jackpotName;
            }
        }
    }

    if(!function_exists('get_field')) {
         add_action('admin_notices', function () {
			$class          = 'notice notice-error';
			$notice_message = 'Jackpot Providers could not be loaded. Because get_field function does not exist. If you are sure ACF plugin is enabled, than an ACF update might caused this.';
			printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($notice_message));
		}
		);
    }

    wp_localize_script('admin-script', 'jackpot_providers', $jackpotProviders);

    // Check get parameter first, then transient, then fallback
    $simulated_time = $_GET['time_value'] ?? (get_transient('px_time_value_' . $current_user->ID) ?? getformatedDatetimeUtc(getNow()));

    // main_params for wp-admin
    wp_localize_script('admin-script', 'main_params', [
        'admin_url'             => admin_url(),
        'ajax_url'              => admin_url('admin-ajax.php'),
        'brand_url'             => brandUrl(),
        'login_url'             => loginUrl(),
        'loggedin'              => player()->isLoggedIn(),
        'locale'                => get_locale(),
        'brand'                 => CURRENT_BRAND,
        'currency'              => CURRENT_CURRENCY,
        'currency_symbol'       => CURRENT_CURRENCY_SYMBOL,
        'timezone'              => CURRENT_TIMEZONE,
        'region'                => CURRENT_REGION,
        'language'              => CURRENT_LANGUAGE,
        'country'               => CURRENT_COUNTRY,
        'tracking'              => TRACKING_PARAMS,
        'debug'                 => DEBUG_MODE,
        'dev'                   => DEV_ENV,
        'local'                 => LOCAL_ENV,
        'proxy'                 => PROXY_ENV,
        'site'                  => SITE_TYPE,
        'sportsbook'            => defined('SPORTSBOOK_PROVIDER') ? SPORTSBOOK_PROVIDER : '',
        'sportsbook_nonce'      => wp_create_nonce('sportsbook-nonce'),
        'simulated_time'        => $simulated_time,
        'wp_nonce'              => wp_create_nonce('acf_autofill_nonce'),
    ]);

    // Inject admin-post script only for editing and creating
    if ($pagenow === 'post.php' || $pagenow === 'post-new.php') {
        wp_enqueue_script('admin-post-script', get_template_directory_uri() . '/dist/admin-post.min.js', [], filemtime(get_template_directory() . '/dist/admin-post.min.js'), true);
    }
}

add_action('admin_enqueue_scripts', 'admin_resources');

// Custom ACF related resources
function acf_admin_resources() {
    $screen = get_current_screen();
    if($screen->base === 'post') {
        $path = "/admin/scripts/";
        wp_enqueue_script('acf-admin', get_template_directory_uri() . $path . 'acf-admin.js', false, filemtime(get_template_directory() . $path . 'acf-admin.js'), true);

        wp_enqueue_script('acf-advanced-validation-message', get_template_directory_uri() . $path . 'acf-advanced-validation-message.js', false, filemtime(get_template_directory() . $path . 'acf-advanced-validation-message.js'), true);

        // ACF Autofill for dev,local,staging only
        if(DEV_ENV || LOCAL_ENV || STAGING_ENV) {
            wp_enqueue_script('acf-autofill', get_template_directory_uri() . $path . 'acf-autofill.js', false, filemtime(get_template_directory() . $path . 'acf-autofill.js'), true);
        }
    }
}
add_action('acf/input/admin_enqueue_scripts', 'acf_admin_resources');

add_filter('admin_body_class', 'add_admin_body_classes');
function add_admin_body_classes($classes) {
    global $current_user;
    foreach ($current_user->roles as $role) {
        $classes .= ' user-role__' . $role;
    }
    return $classes;
}

// iziToast library
// Source: https://izitoast.marcelodolza.com/
function izitoast_admin_notifications() {
    $screen = get_current_screen();

    if ( ! $screen || $screen->base !== 'post' ) {
        return;
    }
    $path = "/admin/scripts/vendor/izitoast/";
    wp_enqueue_script('izitoast-admin', get_template_directory_uri() . $path . 'iziToast.min.js', [], null, true);
    wp_enqueue_style('izitoast-admin-styles', get_template_directory_uri() . $path . 'iziToast.min.css', [], RELEASE_VERSION);
}
add_action('admin_enqueue_scripts', 'izitoast_admin_notifications');


function dequeue_non_critical_dashboard_scripts() {
    $screen = get_current_screen();

    if ( ! $screen || $screen->base !== 'dashboard' ) {
        return;
    }

    // List of critical scripts to keep loaded on dashboard home
    $home_screen_scripts = array(
        'jquery',            // jQuery is a core dependency for most admin pages
        // 'wp-util',           // Essential utility functions
        // 'heartbeat',         // Heartbeat API for auto-save, real-time updates
        // 'updates',           // Handles updates for WordPress
        // 'postbox',           // Toggles metaboxes
        // 'admin-bar',         // Admin bar at the top of the page
        'common',            // Common WordPress admin functions
        'dashboard',         // Core dashboard functionality
        'underscore',        // JavaScript library used by core
        'backbone',          // Backbone.js used by core,
        'site-health',
        'jquery-core',
        'wp-i18n',
        'query-monitor',
        'yoast-seo-admin-global',
        'yoast-seo-dashboard-widget',
        'yoast-seo-wincher-dashboard-widget',
        'admin-script', // Our own js script for admin enqueued with this name
    );
    global $wp_scripts;
    if ( $screen->base == 'dashboard' ) {
        // Loop through registered scripts in the admin area
        foreach ($wp_scripts->registered as $script) {
            if (!in_array($script->handle, $home_screen_scripts)) {
                wp_dequeue_script($script->handle);
            }
        }
    }
}
add_action( 'admin_enqueue_scripts', 'dequeue_non_critical_dashboard_scripts', 100 );

// Add favicon to admin area
function add_favicon_to_admin() {
    echo '<link rel="icon" href="' . get_stylesheet_directory_uri() . '/dist/images/favicon/favicon.png">';
}
add_action('admin_head', 'add_favicon_to_admin');

// Add AJAX handler for random media image, to be used for ACF Autofill
if(DEV_ENV || LOCAL_ENV || STAGING_ENV) {
    add_action('wp_ajax_get_random_media_image', 'get_random_media_image');
}

function get_random_media_image() {
    // Verify nonce
    if (!check_ajax_referer('acf_autofill_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    // Get random image from media library
    $args = array(
        'post_type' => 'attachment',
        'post_mime_type' => 'image',
        'post_status' => 'inherit',
        'posts_per_page' => 1,
        'orderby' => 'rand'
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        $query->the_post();
        $image_data = array(
            'id' => get_the_ID(),
            'url' => wp_get_attachment_url(get_the_ID())
        );
        wp_send_json_success($image_data);
    } else {
        wp_send_json_error('No images found');
    }
    wp_reset_postdata();
}

// Remove update Wordpress notice on prod (ex: WordPress 6.4.2 is available! Please update now.)
if(!DEV_ENV){
    add_action('admin_head', function() {
        remove_action('admin_notices', 'update_nag', 3);
    });
}

// Instead of WordPress Version, show release version and confluence link on Dashboard footer
add_filter('update_footer', function() {
    return 'Version ' . RELEASE_VERSION . ' | <a href="https://comeon.atlassian.net/wiki/spaces/PHNX/overview" target="_blank">Knowledge Base</a>';
}, 11);
