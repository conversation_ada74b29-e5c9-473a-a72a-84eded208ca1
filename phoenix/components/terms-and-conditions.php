<?php
if (!isset($args['have_terms_and_condition'])) {
    $termsConditions = get_field('terms_and_conditions_group');
    // $args taken as variable which contains arguments such as 'place', and it is merged with acf data below

    if(!empty($termsConditions)) {
        $args = array_merge($args, $termsConditions);
    }
}

if (!empty($args['have_terms_and_condition'])) {
    if(empty($args['terms_and_conditions_display_type'])) {
        $args['terms_and_conditions_display_type'] = 'simple';

        if(isFeatureActive('components/terms-and-conditions-modal')) {
            $args['terms_and_conditions_display_type'] = 'popup';
        }
    }

    $placeNotSet = false;
    if (empty($args['place'])) {
        $args['place'] = '';
        $placeNotSet = true;
    }

    $slug = sanitize_title($args['terms_and_conditions_title']);
    $is_bad_slug = preg_match('/[一-龠]+|[ぁ-ゔ]+|[ァ-ヴー]+|[々〆〤]+/u', (string) $args['terms_and_conditions_title']);
    if($is_bad_slug){
        $slug = strToHex($args['terms_and_conditions_title']);
    }

    global $termsAndConditionCount;
    if (!empty($termsAndConditionCount)) {
        $slug .= '-' . $termsAndConditionCount++;
    } else {
        $termsAndConditionCount = 1;
    }

    $args['terms_and_conditions_slug'] = $slug;
    $args['terms_and_conditions_id'] = 't-c-' . $slug;

    if( !empty($args['terms_and_conditions_background_color']) ) {
        addBackgroundInlineStyle(
            '#'. $args['terms_and_conditions_id'],
            [
                'background_type' => true, // false => image,  true => color
                'background_color' => $args['terms_and_conditions_background_color']
            ]
        );
    }

    if ($args['place'] === 'below_cta' || $placeNotSet) {
        if ($args['terms_and_conditions_display_type'] === 'popup') {
            get_template_part('components/terms-and-conditions-modal', null, $args);
        }
        if ($args['terms_and_conditions_display_type'] === 'sticky') {
            get_template_part('components/terms-and-conditions-sticky', null, $args);
        }
        if ($args['terms_and_conditions_display_type'] === 'sticky-sidebar') {
            get_template_part('components/terms-and-conditions-sticky-sidebar', null, $args);
        }
        if ($args['terms_and_conditions_display_type'] === 'dropdown') {
            get_template_part('components/terms-and-conditions-dropdown', null, $args);
        }
    }
    if ($args['place'] === 'below_content' || $placeNotSet) {
        if ($args['terms_and_conditions_display_type'] === 'simple') {
            get_template_part('components/terms-and-conditions-simple', null, $args);
        }
    }
}
?>