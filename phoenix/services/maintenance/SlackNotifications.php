<?php
// LOCAL_ENV constant can't be used here, since it's defined way after the inclusion of this file
if(!empty($_SERVER['SERVER_NAME']) && !preg_match('/localhost/', (string) $_SERVER['SERVER_NAME'])) {
    add_filter('wp_php_error_message', 'send_notification_to_slack', 10, 2);
}

function send_notification_to_slack($message, $error)
{
    $slack_notifications_endpoint      = '*********************************************************************************'; // Endpoint to #phoenix-errors channel only
    $last_notified_error_transient_key = 'px_slack_last_notified_error';
    $last_notified_error               = get_transient($last_notified_error_transient_key);
    $transient_storage_time_in_seconds = 1 * 3600; // 1 hour
    $current_time_in_seconds           = time();
    $current_error_message             = str_replace('Uncaught', '❌', (string) $error['message']);
    $current_error_message             = $current_error_message . ' at line ' . $error['line'];
    $notify_signal                     = false;

    $url_throwing_error = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

    $post_id              = from_url_to_postid($url_throwing_error);
    if(!empty($post_id)) {
        $post                 = get_post($post_id);
        if(!empty($post)) {
            $post_author          = get_user_by('id', $post->post_author) ?? '-';
        }
    }
    $post_related_message = [];  // fallback;

    // Remove php stack trace from the error message
    $current_error_message = strstr($current_error_message, 'Stack trace', true) ?: $current_error_message;
    if (!empty($last_notified_error)) {
        if (is_array($last_notified_error)) {
            if (array_key_exists('message', $last_notified_error)) {
                if ($last_notified_error['message'] !== $current_error_message) {

                    $last_notified_error['message']        = $current_error_message;
                    $last_notified_error['notified_at']    = $current_time_in_seconds;

                    set_transient($last_notified_error_transient_key, $last_notified_error, $transient_storage_time_in_seconds);

                    $notify_signal = true;
                } else {
                    // Don't send notification for same error in 1 hour
                    if ($current_time_in_seconds > ($last_notified_error['notified_at'] + $transient_storage_time_in_seconds)) {
                        $last_notified_error['message']        = $current_error_message;
                        $last_notified_error['notified_at']    = $current_time_in_seconds;
                        set_transient($last_notified_error_transient_key, $last_notified_error, $transient_storage_time_in_seconds);
                        $notify_signal = true;
                    }
                }
            }
        }
    } else {
        $last_notified_error = [
            'message' => $current_error_message,
            'notified_at' => $current_time_in_seconds
        ];
        set_transient($last_notified_error_transient_key, $last_notified_error, $transient_storage_time_in_seconds);
    }

    if ($notify_signal) {

        $user_id = get_current_user_id();
        if ( is_user_logged_in() ) {
            $user = get_userdata( $user_id );
            $display_name = sanitize_text_field( $user->display_name );
        } else {
            $display_name = 'Visitor';
        }

        $error_triggered_by = '🔎 Error triggered by ('.$display_name.') on '.$_SERVER[ 'REQUEST_URI' ];

        // If page is not a dashboard page, then get post details
        if(!str_contains($url_throwing_error, '/wp-admin/')) {
            if(!empty($post_id) && !empty($post)) {
                $post_related_message = [
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Date:* " . $post->post_date
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Modified:* " . $post->post_modified
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Type:* " . $post->post_type
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post ID:* " . $post->ID
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Author:* " . ($post_author->display_name ?: '')
                    ]
                ];
            }
        }

        $notification_body = [
            "blocks" => [
                [
                    "type" => "section",
                    "fields" => [
                        [
                            "type" => "mrkdwn",
                            "text" => "*Domain:* " . get_bloginfo('url')
                        ],
                        [
                            "type" => "mrkdwn",
                            "text" => "*WP Version:* " . get_bloginfo('version')
                        ],
                        [
                            "type" => "mrkdwn",
                            "text" => "*Release Version:* " . RELEASE_VERSION
                        ],
                        [
                            "type" => "mrkdwn",
                            "text" => "*PHP Version:* " . phpversion()
                        ]
                    ]
                ],
                [
                    "type" => "section",
                    "fields" => array_merge(
                        [
                            [
                                "type" => "mrkdwn",
                                "text" => $current_error_message
                            ],
                            [
                                "type" => "mrkdwn",
                                "text" => $error_triggered_by
                            ],
                        ],
                        $post_related_message
                    )
                ],
                [
                    "type" => "divider",
                ],
            ]
        ];

        $notification_body = wp_json_encode($notification_body);

        $options = [
            'body'        => $notification_body,
            'headers'     => [
                'Content-Type' => 'application/json',
            ],
            'timeout'     => 30,
            'httpversion' => '1.0',
            'sslverify'   => false,
            'data_format' => 'body',
        ];

        wp_remote_post($slack_notifications_endpoint, $options);
    }

    return $message;
}
