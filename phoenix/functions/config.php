<?php

/**
 * Check if given feature is available in the instance or not
 *
 * @param string $featureName full name of the feature
 * naming are:
 *      games/quiz
 *      games/letter-game
 *      campaigns/acq
 *      campaigns/acq/jackpot-games
 *      ...
 *      campaigns/crm
 *      campaigns/crm/left-and-right
 *      ...
 * */
function isFeatureActive($featureName)
{
    global $configArray;

    if (trim($featureName) == '') {
        return false;
    }
    $featureParts = explode('/', trim($featureName, '/'));

    $featureConfig = $configArray;
    foreach ($featureParts as $featurePart) {
        if (!isset($featureConfig[$featurePart])) {
            return false;
        }
        $featureConfig = $featureConfig[$featurePart];
    }

    return isFeatureActiveRecursively($featureConfig);
}

function isFeatureActiveRecursively($config)
{
    if (is_array($config)) {
        foreach ($config as $subConfig) {
            if (isFeatureActiveRecursively($subConfig)) {
                return true;
            }
        }
        return false;
    }

    return $config;
}

function brandUrl() {
    global $apiBase;

    if(DEV_ENV || STAGING_ENV || LOCAL_ENV) {
        // Check for API Base Override from Phoenix Toolkit
        $override = get_transient('px_api_base_override');
        if (!empty($override)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE && function_exists('do_action')) {
                do_action('qm/info', 'brandUrl() returning Phoenix Toolkit override: ' . $override);
            }
            return $override;
        }
    }

    if(!empty($apiBase)) {
        $returnUrl = '';

        if (DEV_ENV || LOCAL_ENV) {
            if(!empty($apiBase[SITE_TYPE_DEV])) {
                $returnUrl = $apiBase[SITE_TYPE_DEV];
            } elseif(!empty($apiBase[SITE_TYPE_STAGING])) {
                $returnUrl = $apiBase[SITE_TYPE_STAGING];
            }
        }

        if (STAGING_ENV && !empty($apiBase[SITE_TYPE_STAGING])) {
            if(!preg_match('/cleverdolphin|.one/', $_SERVER['SERVER_NAME'])) {
                // If domain is not .one domain or cleverdolphin, then its .com (example: phnxstg.comeon.com)
                $returnUrl = $apiBase[SITE_TYPE_PRODUCTION];
            } else {
                $returnUrl = $apiBase[SITE_TYPE_STAGING];
            }
        }

        if (empty($returnUrl)) {
            $returnUrl = $apiBase[SITE_TYPE_PRODUCTION];
        }

        if (defined('DEBUG_MODE') && DEBUG_MODE && function_exists('do_action')) {
            $environment = DEV_ENV ? 'DEV' : (LOCAL_ENV ? 'LOCAL' : (STAGING_ENV ? 'STAGING' : 'PRODUCTION'));
            do_action('qm/info', 'brandUrl() returning for ' . $environment . ' environment: ' . $returnUrl);
            do_action('qm/debug', 'Available API Base URLs: ' . json_encode($apiBase));
        }

        return $returnUrl;
    }

    if (defined('DEBUG_MODE') && DEBUG_MODE && function_exists('do_action')) {
        do_action('qm/error', 'brandUrl() - $apiBase global variable is empty or not set');
    }

    return 0;
}

function registerUrl() {
    return brandUrl() . '/casino/explore/?sidebar=register';
}

function loginUrl() {
    return brandUrl() . '/casino/explore/?sidebar=login';
}

function logoutUrl() {
    return brandUrl() . '/casino/explore/';
}

function logoUrl() {
    return brandUrl() . '/' . CURRENT_REGION;
}
