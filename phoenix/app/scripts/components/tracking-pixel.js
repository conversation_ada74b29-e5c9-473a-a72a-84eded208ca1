// Pixel tracking script
// if (!window.location.hostname.includes('localhost') && !window.location.hostname.includes('phnx')) {
//     let url = "https://www." + window.location.hostname.split('.')[1] + "." + window.location.hostname.split('.')[2] + "/tracking";
//     const scriptElement = document.createElement('script');

//     fetch(url, {
//         method: "GET",
//         credentials: "include"
//     }).then(res => res.json())
//         .then(response => {
//             let scriptHTML = response.result['site'];
//             if (scriptHTML === undefined) {
//                 console.error('Cannot load pixel tracking');
//             } else {
//                 scriptHTML = scriptHTML.replace(/(<([^>]+)>)/ig, '');
//                 scriptHTML = scriptHTML.replace(
//                     "'pixel_device': 'Mobile'",
//                     "'pixel_device': " +
//                     (/Mobi|Android/i.test(navigator.userAgent)
//                         ? "'Mobile'"
//                         : "'Desktop'")
//                 );
//                 scriptElement.innerHTML = scriptHTML;
//                 document.body.appendChild(scriptElement);
//             }
//         })
//         .catch(err => { throw err });
// }