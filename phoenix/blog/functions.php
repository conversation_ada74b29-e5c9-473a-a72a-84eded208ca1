<?php

include "widgets/offer.php";
include "widgets/trending.php";
include "widgets/twitter.php";
include "widgets/social.php";
//include "widgets/popular-tags.php";

// Ignore the sticky posts in the default query
function ignore_sticky_posts()
{
    global $wp_query;

    if ($wp_query->is_home() && $wp_query->is_main_query()) {
        $sticky = get_option('sticky_posts');
        $wp_query->set('post__not_in', $sticky);
        $wp_query->set('is_post_page', 1);
        $wp_query->set('posts_per_page', 6);
    }
}

add_filter('pre_get_posts', 'ignore_sticky_posts', 1);

register_nav_menus([
    'top-menu' => esc_html__('Blog Top Menu', 'phoenix'),
    'blog-menu' => esc_html__('Blog Categories Menu', 'phoenix')
]);

// Blog script
function blog_script() {
    if(is_blog()) {
        wp_register_script('blog', get_template_directory_uri() . '/blog/blog.js', [], filemtime(get_template_directory() . '/blog/blog.js'), true);
        wp_enqueue_script('blog');
    }
}
add_action('wp_enqueue_scripts', 'blog_script');

// Show more posts script
function show_more_script()
{

    global $post;
    if(isset($post->post_type) && $post->post_type == 'post'){
        global $wp_query;
        wp_register_script('comeon-show-more', get_template_directory_uri() . '/blog/showmore.js', [], filemtime(get_template_directory() . '/blog/showmore.js'), true);

        wp_localize_script('comeon-show-more', 'show_more_params', [
            'ajax_url' => admin_url( 'admin-ajax.php' ),
            'posts' => json_encode($wp_query->query_vars),
            'current_page' => get_query_var('paged') ?: 1,
            'max_page' => $wp_query->max_num_pages,
            'posts_per_page' => 3
        ]);

        wp_enqueue_script('comeon-show-more');
    }
}
add_action('wp_enqueue_scripts', 'show_more_script');

// AJAX handler for show more posts
function show_more()
{
    $args = json_decode(stripslashes((string) $_POST['query']), true);
    $args['paged'] = $_POST['page'] + 1;
    $args['post_status'] = 'publish';
    $args['post_type'] = 'post';
    $args['ignore_sticky_posts'] = 1;
    $args['posts_per_page'] = 6;
    query_posts($args);
    if (have_posts()) :
        while (have_posts()) : the_post();
            get_template_part('blog/content/content');
        endwhile;
    endif;
    die;
}
add_action('wp_ajax_show_more', 'show_more');
add_action('wp_ajax_nopriv_show_more', 'show_more');

// Get only the first 20 words
function excerpt_length_limit($length)
{
    // If it's a sticky post, then less words should show
    if (is_sticky()) :
        return 15;
    endif;
    return 40;
}

add_filter('excerpt_length', 'excerpt_length_limit', 999);


// Replaces the excerpt read more [...]
function new_excerpt_more($more)
{
    global $post;
    return '...';
}

add_filter('excerpt_more', 'new_excerpt_more');

add_image_size('home-page-small-thumbnail', 360, 250, false);
add_image_size('home-page-sticky-thumbnail', 510, 290, false);
add_image_size('post-single', 0, 400, true);

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function phoenix_widgets_init()
{
    register_sidebar([
        'name' => esc_html__('Blog Sidebar', 'phoenix'),
        'id' => 'sidebar-1',
        'description' => esc_html__('Anything added here will show up in the sidebar on all blog posts.', 'phoenix'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h2 class="widget-title">',
        'after_title' => '</h2>'
    ]);
}

add_action('widgets_init', 'phoenix_widgets_init');
function offers_post_type()
{
    register_post_type(
        'offers',
        [
            'labels' => [
                'name' => __('Blog Offers'),
                'singular_name' => __('Offer')
            ],
            'exclude_from_search' => true,
            'public' => true,
            'has_archive' => true,
            'menu_icon' => 'dashicons-tickets-alt',
            'menu_position' => 5,
            'supports' => ['title', 'editor', 'thumbnail'],
            'publicly_queryable' => false
        ]
    );
}

add_action('init', 'offers_post_type');

// Generate social media share link
function getShareLink($platform, $link) {
    $finalLink = '';

    $finalLink = match (strtolower((string) $platform)) {
        'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . $link,
        'linkedin' => 'http://www.linkedin.com/shareArticle?mini=true&url=' . $link,
        'telegram' => 'https://t.me/share/url?url=' . $link,
        'whatsapp' => 'https://api.whatsapp.com/send?text=' . $link,
        'twitter' => 'https://twitter.com/share?url=' . $link,
        'line' => 'https://social-plugins.line.me/lineit/share?url=' . $link,
        default => $finalLink,
    };

    return $finalLink;
}

function offers_posts_columns($columns)
{
$columns['placement'] = 'Placement';
    return $columns;
}

add_filter('manage_offers_posts_columns', 'offers_posts_columns');


function offers_columns($column, $post_id)
{
    switch ($column) {
        case 'placement':
            switch (get_field('sidebar_offer', $post_id)){
                case '1':
                case 'true':
                case 'sidebar':
                    echo 'Sidebar offer';
                    break;
                case '0':
                case 'false':
                case 'content':
                    echo 'First page left column offer';
                    break;
                case 'fullscreen':
                    echo 'First page fullscreen offer';
                    break;
            }
            break;
    }
}

add_action('manage_offers_posts_custom_column', 'offers_columns', 10, 2);
