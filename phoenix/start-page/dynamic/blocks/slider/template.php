<?php
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);
$slides = get_field('block_slides');
$parallax = get_field('block_slides_parallax');
$scroll_trigger = get_field('block_slides_gsap_scroll_trigger');
$blockHeightMobile = get_field('main_block_mobile_height');
$blockHeightDesktop = get_field('main_block_desktop_height');

if(!empty($scroll_trigger)) {
    includeGSAP();
}

if (!empty($slides) && is_array($slides)) {
    $slides = array_map(function ($slide) {
        if (empty($slide['block_content']['campaign_header']) || empty($slide['block_content']['button_url']) || empty($slide['block_content']['button_text'])) {
            return null;
        }
        return $slide;
    }, $slides);
    $slides = array_filter($slides);
}
get_template_part('start-page/dynamic/parts/preview');
if (!empty($slides) && is_array($slides)) : ?>
    <?php
    $spSlideClass = getClass([
        'sp-block sp-block-slider content-stretchable block-stretchable swiper-container',
        'sp-block-slider--' . $block['id'],
        $block['className'],
        (!empty($parallax) ? 'swiper-container--parallax' : '')
    ]);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spSlideClass; ?>">
        <div class="swiper js-slider-sticky-button" data-swiper-parallax-attr="<?= $parallax; ?>" data-swiper-gsap-scroll-trigger="<?= $scroll_trigger; ?>" data-swiper-autoplay="<?= get_field('block_slides_autoplay_speed'); ?>" data-swiper-touch-move="<?= get_field('block_slides_swipe') ? 'true' : 'false'; ?>">
            <?php if (!empty($parallax)):
                $parallax_bg = '';
                foreach ($slides as $key => $slide) :
                    // First found block background will be used on desktop for parallax.
                    if (!empty($slide['block_background']['background_image'])) :
                        $parallax_bg .= $slide['block_background']['background_image'];
                        if (wp_is_mobile()) :
                            // First found mobile background will be used on mobile for parallax.
                            $parallax_bg_mobile = $slide['block_background']['mobile_background_image'];
                            if (!empty($parallax_bg_mobile)) :
                                $parallax_bg = '';
                                $parallax_bg .= $parallax_bg_mobile;
                            endif;
                        endif;
                        break;
                    endif;
                endforeach;
            ?>
                <div class="parallax-bg" style="background-image: url(&quot;<?= $parallax_bg; ?>&quot;); transform: translate3d(0%, 0px, 0px);" data-swiper-parallax="-23%"></div>
            <?php endif; ?>
            <div class="swiper-wrapper">
                <?php foreach ($slides as $key => $slide) : ?>
                    <?php addBackgroundInlineStyle('.sp-block-slider--' . $block['id'] . ' .sp-block-slider__slide--' . $key, $slide['block_background']); ?>
                    <?php
                    $slideClasses = getClass([
                        'background content-stretchable block-stretchable swiper-slide',
                        'sp-block-slider__slide--' . $key,
                        'sp-block-slider__slide sp-block-main',
                        'sp-block-main--vh-mobile-' . $blockHeightMobile,
                        'sp-block-main--vh-desktop-' . $blockHeightDesktop
                    ]);
                    ?>
                    <section class="<?= $slideClasses; ?>" data-content="dynamic-slide-<?= $key; ?>">
                        <?php get_template_part('campaigns/parts/background-video', null, $slide['block_background']); ?>
                        <?php get_template_part('campaigns/parts/background-lottie', null, $slide['block_background']); ?>
                        <?php
                        $contentContainerClass = getClass([
                            'content-margin campaign__content container wrapper sp-block-slider__content',
                            [
                                'condition' => ($slide['layout_style'] === 'centered'),
                                'name' => 'campaign__content--centered',
                                'else-name' => 'wrapper--md-80',
                            ],
                            [
                                'condition' => ($slide['layout_style'] === 'centered' &&
                                    empty($slide['layout_settings']['full_width'])),
                                'name' => 'wrapper--tight'
                            ],
                            [
                                'condition' => !empty($slide['block_content']['invert_text_color']),
                                'name' => 'campaign__content--invert-font-color',
                                'else-name' => 'campaign__content--normal-font-color',
                            ],
                            [
                                'condition' => !empty($slide['layout_settings']['full_width']),
                                'name' => 'wrapper--wider'
                            ]
                        ]);
                        ?>
                        <div class="<?= $contentContainerClass; ?>">
                            <?php
                            // Backwards compatibility with 'grid-left-and-right' class fallback
                            $layoutStyleClass = 'grid-' . ($slide['layout_style'] ?? 'left-and-right');

                            $gridLeftAndRightContainerClass = getClass([
                                $layoutStyleClass,
                                [
                                    'condition' => !empty($slide['block_content']['image_above_heading']),
                                    'name' => $layoutStyleClass . '--has-image-above-heading',
                                ],
                                [
                                    'condition' => 'centered' !== $slide['layout_style'],
                                    'name' => $layoutStyleClass . '--cols',
                                ],
                                [
                                    'condition' => !empty($slide['layout_settings']['columns_size']),
                                    'name' => $layoutStyleClass . '--' . $slide['layout_settings']['columns_size'],
                                ]
                            ]);
                            ?>
                            <div class="<?= $gridLeftAndRightContainerClass; ?>">
                                <!-- content -->
                                <?php
                                $gridLeftColumnClass = getClass([
                                    $layoutStyleClass . '__left',
                                    'sp-block__animation',
                                    'sp-block__animation--fade-in-left',
                                    [
                                        'condition' => !empty($slide['block_content']['vertical_alignment']),
                                        'name' => $layoutStyleClass . '__left--' . $slide['block_content']['vertical_alignment'],
                                    ]
                                ]);
                                ?>
                                <div class="<?= $gridLeftColumnClass; ?>">
                                    <?php if (!empty($slide['block_content']['tags'])) : ?>
                                        <div class="tags">
                                            <?php foreach ($slide['block_content']['tags'] as $tag) : ?>
                                                <span class="tag">
                                                    <?= $tag['tag'] ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php
                                    global $content;
                                    $content = $slide['block_content'];
                                    ?>
                                    <?php get_template_part('campaigns/parts/image-above-title'); ?>
                                    <?php get_template_part('campaigns/parts/title'); ?>
                                    <?php get_template_part('campaigns/parts/subtitle'); ?>
                                    <?php get_template_part('campaigns/parts/description'); ?>
                                    <?php get_template_part('components/terms-and-conditions', null, array_merge($slide['terms_and_conditions_group'], ['place' => 'above_cta'])); ?>
                                    <?php ob_start(); ?>
                                    <?php
                                    $buttonClass = getClass([
                                        'btn',
                                        [
                                            'condition' => !empty($slide['block_content']['button_color']),
                                            'name' => 'btn--' . $slide['block_content']['button_color'],
                                        ],
                                        [
                                            'condition' => !empty($slide['block_content']['button_size']),
                                            'name' => 'btn--' . $slide['block_content']['button_size'],
                                        ],
                                        [
                                            'condition' => !empty($slide['block_content']['button_animation']),
                                            'name' => 'btn--animation-' . $slide['block_content']['button_animation'],
                                        ],
                                        [
                                            'condition' => !empty($slide['block_content']['button_tracking']),
                                            'name' => 'js-' . $slide['block_content']['button_tracking'],
                                            'else-name' => 'js-button-claim',
                                        ]
                                    ]);
                                    ?>
                                    <a href="<?= $slide['block_content']['button_url']; ?>" class="<?= $buttonClass; ?>">
                                        <?= $slide['block_content']['button_text']; ?>
                                    </a>
                                    <?php
                                    $stickyButtonElement = ob_get_clean();
                                    get_template_part('campaigns/parts/cta-button-sticky', null, [
                                        'button' => $stickyButtonElement,
                                        'sticky' => empty($slide['block_content']['not_sticky_mobile_button']),
                                        'content' => 'dynamic-slide-' . $key,
                                        'visible' => !$key,
                                    ]);
                                    ?>
                                    <?php get_template_part('components/terms-and-conditions', null, array_merge($slide['terms_and_conditions_group'], ['place' => 'below_cta'])); ?>
                                </div>
                                <div class="<?= $layoutStyleClass; ?>__right sp-block__animation sp-block__animation--fade-in-right">
                                    <?php
                                    if (!empty($slide['block_image'])) {
                                        $imageArgs = array_merge(
                                            $slide['block_image'],
                                            [
                                                'type' => ($slide['layout_style'] === 'centered' ? 'small' : ''),
                                                'alt' => $slide['block_content']['campaign_header'],
                                                'link' => $slide['block_content']['button_url']
                                            ]
                                        );
                                        get_template_part('campaigns/parts/offer-media', null, $imageArgs);
                                    }
                                    ?>
                                </div>
                            </div>
                            <?php get_template_part('components/terms-and-conditions', null, array_merge($slide['terms_and_conditions_group'], ['place' => 'below_content'])); ?>
                        </div>
                    </section>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="swiper-button-prev swiper-button-prev--large swiper-button-prev--inner"><?= vector('arrow-left'); ?></div>
        <div class="swiper-button-next swiper-button-prev--large swiper-button-prev--inner"><?= vector('arrow-right'); ?></div>

        <?php if (!empty(get_field('block_slides_navigation'))) : ?>
            <div class="sp-block-slider__summary swiper-thumbs swiper--equal-height-slides">
                <div class="swiper-wrapper">
                    <?php foreach ($slides as $key => $slide) : ?>
                        <div class="sp-block-slider__summary__item swiper-slide">
                            <?php if (!empty($slide['block_content']['tags'])) : ?>
                                <span class="tag">
                                    <?= $slide['block_content']['tags'][0]['tag'] ?>
                                </span>
                            <?php endif; ?>
                            <h2>
                                <?= $slide['block_content']['campaign_header'] ?>
                            </h2>
                            <a href="<?= $slide['block_content']['button_url'] ?>" class="btn btn--small btn--rounded btn--white btn--outlined">
                                <?= $slide['block_content']['button_text'] ?>
                            </a>
                            <?php if (!empty($slide['terms_and_conditions_group']['have_terms_and_condition'])) : ?>
                                <span class="t-and-c">
                                    <?= get_field('block_slides_navigation_terms_conditions_apply') ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php else : ?>
            <div class="swiper-pagination swiper-pagination--inner"></div>
        <?php endif; ?>
    </section>
<?php
endif;
