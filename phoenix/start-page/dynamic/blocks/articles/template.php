<?php
getBlockCommonFields();
$articles = get_field('block_articles');
$readMore = get_field('block_read_more');
if (!empty($articles) && is_array($articles)) {
    $articles = array_map(function ($article) {
        if (is_array($article) && !all_have_values(...$article)) {
            return null;
        }
        return $article;
    }, $articles);

    $articles = array_filter($articles);
}

if (!empty($articles) && is_array($articles)) {
    ob_start();
?>
    <div class="sp-block-article-slider wrapper swiper-container">
        <div class="swiper swiper--equal-height-slides">
            <div class="swiper-wrapper">
                <?php foreach ($articles as $article) : ?>
                    <a href="<?= $article['url']; ?>" class="sp-block-article-slider__item swiper-slide">
                        <img src="<?= $article['image']; ?>" alt="<?= wp_strip_all_tags($article['title']); ?>" class="sp-block-article-slider__image" />
                        <h3 class="sp-block-article-slider__title"><?= $article['title']; ?></h3>
                        <div class="sp-block-article-slider__text"><?= $article['text']; ?></div>
                        <div class="sp-block-article-slider__read-more"><?= $readMore ?> <?= vector('arrow-right'); ?></div>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="swiper-button-prev swiper-button-prev--small"><?= vector('left'); ?></div>
        <div class="swiper-button-next swiper-button-prev--small"><?= vector('right'); ?></div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
