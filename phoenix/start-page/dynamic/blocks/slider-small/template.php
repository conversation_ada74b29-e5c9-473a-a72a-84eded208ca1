<?php
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);
$slides = get_field('block_slides');
$blockHeightMobile = get_field('main_block_mobile_height');
$blockHeightDesktop = get_field('main_block_desktop_height');

get_template_part('start-page/dynamic/parts/preview');
if (!empty($slides) && is_array($slides)) : ?>
    <?php
    $spSlideClass = getClass([
        'sp-block sp-block-slider content-stretchable block-stretchable swiper-container',
        'sp-block-slider--' . $block['id'],
        $block['className'],
    ]);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spSlideClass; ?>">
        <div class="swiper js-slider-sticky-button" data-swiper-autoplay="<?= get_field('block_slides_autoplay_speed'); ?>" data-swiper-touch-move="<?= get_field('block_slides_swipe') ? 'true' : 'false'; ?>">
            <div class="swiper-wrapper">
                <?php foreach ($slides as $key => $slide) : ?>
                    <?php addBackgroundInlineStyle('.sp-block-slider--' . $block['id'] . ' .sp-block-slider__slide--' . $key, $slide['block_background']); ?>
                    <?php
                    $slideClasses = getClass([
                        'background content-stretchable block-stretchable swiper-slide',
                        'sp-block-slider__slide--' . $key,
                        'sp-block-slider__slide sp-block-main',
                        'sp-block-main--vh-mobile-' . $blockHeightMobile,
                        'sp-block-main--vh-desktop-' . $blockHeightDesktop
                    ]);
                    ?>
                    <section class="<?= $slideClasses; ?>">
                        <?php get_template_part('campaigns/parts/background-video', null, $slide['block_background']); ?>
                        <?php get_template_part('campaigns/parts/background-lottie', null, $slide['block_background']); ?>

                        <?php
                        global $content;
                        $content = $slide['block_content'];
                        ?>
                        <?php
                        $contentContainerClass = getClass([
                            'content-margin content-margin--small campaign__content container wrapper sp-block-slider__content',
                            [
                                'condition' => !empty($content['invert_text_color']),
                                'name' => 'campaign__content--invert-font-color',
                                'else-name' => 'campaign__content--normal-font-color',
                            ]
                        ]);
                        ?>
                        <div class="<?= $contentContainerClass; ?>">
                            <!-- content -->
                            <?php
                            $contentClass = getClass([
                                'text-center',
                                'sp-block__animation',
                                'sp-block__animation--fade-in-center',
                            ]);
                            ?>
                            <div class="<?= $contentClass; ?>">

                                <?php get_template_part('campaigns/parts/title', null, ['class' => 'campaign__heading--small']); ?>
                                <?php get_template_part('campaigns/parts/subtitle', null, ['class' => 'campaign__subheading--small']); ?>
                                <?php get_template_part('campaigns/parts/description'); ?>

                                <?php if (!empty($content['tags'])) : ?>
                                    <div class="sp-block-slider__tags">
                                        <?php foreach ($content['tags'] as $tag) : ?>
                                            <div class="sp-block-slider__tag">
                                                <?= $tag['tag'] ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if ($content['enable_button']) : ?>
                                <div class="btn__wrapper">
                                    <?php
                                    $button = $content['cta_button'];
                                    $buttonClass = getClass([
                                        'btn',
                                        [
                                            'condition' => !empty($button['button_color']),
                                            'name' => 'btn--' . $button['button_color'],
                                        ],
                                        [
                                            'condition' => !empty($button['button_size']),
                                            'name' => 'btn--' . $button['button_size'],
                                        ],
                                        [
                                            'condition' => !empty($button['button_animation']),
                                            'name' => 'btn--animation-' . $button['button_animation'],
                                        ],
                                        [
                                            'condition' => !empty($button['button_tracking']),
                                            'name' => 'js-' . $button['button_tracking'],
                                            'else-name' => 'js-button-claim',
                                        ]
                                    ]);
                                    ?>
                                    <a href="<?= $button['button_url']; ?>" class="<?= $buttonClass; ?>" target="_blank">
                                        <?= $button['button_text']; ?>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </section>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="swiper-button-prev swiper-button-prev--large swiper-button-prev--inner"><?= vector('arrow-left'); ?></div>
        <div class="swiper-button-next swiper-button-prev--large swiper-button-prev--inner"><?= vector('arrow-right'); ?></div>

        <div class="swiper-pagination swiper-pagination--inner"></div>
    </section>
<?php
endif;
