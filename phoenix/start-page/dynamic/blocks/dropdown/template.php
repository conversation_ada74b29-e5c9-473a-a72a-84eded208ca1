<?php
getBlockCommonFields();

$dropdowns = get_field('block_dropdowns');

if (!empty($dropdowns) && is_array($dropdowns)) {
    $dropdowns = array_map(function ($dropdown) {
        if (is_array($dropdown) && !all_have_values(...$dropdown)) {
            return null;
        }
        return $dropdown;
    }, $dropdowns);

    $dropdowns = array_filter($dropdowns);
}
ob_start();
?>
<div class="wrapper">
    <div class="sp-block-dropdown sp-block__animation sp-block__animation--fade-in-center">
        <?php foreach ($dropdowns as $dropdown) : ?>
            <?php get_template_part('components/dropdown', null, [
                'title' => $dropdown['title'],
                'text' => $dropdown['text'],
                'class' => 'sp-block-dropdown__item sp-block__card'
            ]); ?>
        <?php endforeach; ?>
    </div>
</div>
<?php
$block['content'] = ob_get_clean();

get_template_part('start-page/dynamic/parts/block', null, $block);
