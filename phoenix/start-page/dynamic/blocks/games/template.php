<?php
getBlockCommonFields();
$gameSliders = get_field('block_games_sliders');

if (!empty($gameSliders) && is_array($gameSliders)) {
    foreach ($gameSliders as $key => $gameSlider) {
        if (!empty($gameSlider['enable_casino_games'] && !empty($gameSlider['games_list']))) {
            $gameSliders[$key]['games_list'] = CasinoGame::getGames($gameSlider['games_list']);
        }
    }

    $gameSliders = array_map(function ($gameSlider) {
        $array = $gameSlider['games_list'];
        $array = array_map(function ($game) {
            if (empty($game['game_title']) || empty($game['game_image'])) {
                return null;
            }
            return $game;
        }, $array);

        $gameSlider['games_list'] = array_filter($array);

        if (empty($gameSlider['games_list'])) {
            return null;
        }

        return $gameSlider;
    }, $gameSliders);

    $gameSliders = array_filter($gameSliders);
}

if (!empty($gameSliders)) {
    ob_start();
?>
    <div class="wrapper">
        <div class="sp-block-games">
            <?php foreach ($gameSliders as $gameSlider) {
                match ($gameSlider['display']) {
                    'grid' => get_template_part('components/game-grid', null, [
                        'size'   => $gameSlider['size'],
                        'title'  => $gameSlider['slider_title'],
                        'games' => $gameSlider['games_list'],
                        'cta_text' => !empty($gameSlider['custom_cta_text']) ? $gameSlider['custom_cta_text'] : get_field_tweaked('play_now', 'option'),
                    ]),
                    default => get_template_part('components/game-slider', null, [
                        'size'   => $gameSlider['size'],
                        'title'  => $gameSlider['slider_title'],
                        'slides' => $gameSlider['games_list'],
                        'cta_text' => !empty($gameSlider['custom_cta_text']) ? $gameSlider['custom_cta_text'] : get_field_tweaked('play_now', 'option'),
                    ]),
                };
            } ?>
        </div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
