<?php
global $blockContent, $blockButton;
getBlockCommonFields();
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);
$type = get_field('block_type');
$fontColor = 'normal-font-color';
if (get_field('invert_text_color')) {
    $fontColor = 'invert-font-color';
}
$background = get_field('block_background');
$enableStickyButton = get_field('block_enable_sticky_button') ?? false;
$enableProducts = get_field('block_enable_products') ?? true;
$products = get_field('block_products');
$block_image = get_field('block_image');
addBackgroundInlineStyle('.sp-block--' . $block['id'], $background);

if (!empty($products) && is_array($products)) {
    $products = array_map(function ($product) {
        if (is_array($product) && !all_have_values(...$product)) {
            return null;
        }
        return $product;
    }, $products);

    $products = array_filter($products);
}

get_template_part('start-page/dynamic/parts/preview');

$spBlockClass = getClass([
    'sp-block sp-block-main',
    'sp-block--' . $type,
    'sp-block--' . $fontColor,
    'sp-block--' . $block['id'],
    'sp-block-main--vh-mobile-' . get_field('main_block_mobile_height'),
    'sp-block-main--vh-desktop-' . get_field('main_block_desktop_height'),
    $block['className'],
    'background content-stretchable block-stretchable',
    'sp-block-products__container',
]);

if (at_least_one_has_value($blockContent, array_keys_have_values($blockButton, ['button_text', 'button_url']))) : ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spBlockClass; ?>">
        <?php get_template_part('campaigns/parts/background-video', null, $background); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $background); ?>
        <div class="container sp-block__container">
            <div class="wrapper">

                <div class="sp-block__animation sp-block__animation--fade-in-top">
                    <?php get_template_part('start-page/dynamic/parts/block', 'content'); ?>

                    <?php get_template_part('components/terms-and-conditions', null, ['place' => 'above_cta']); ?>
                </div>

                <div class="sp-block__animation sp-block__animation--fade-in-center text-center">
                    <?php if (!empty($block_image)) : ?>
                        <div class="sp-block__image">
                            <img src="<?= $block_image; ?>" alt="<?= wp_strip_all_tags($blockContent['title']); ?>" height="450" width="450" />
                        </div>
                    <?php endif; ?>
                    <?php get_template_part('start-page/dynamic/parts/block', 'cta', ['sticky' => $enableStickyButton, 'block-id' => $block['id']]); ?>
                </div>

                <?php if (!empty($products) && $enableProducts) : ?>
                    <div class="sp-block-products sp-block__animation sp-block__animation--fade-in-bottom<?= (count($products) > 3 ? ' sp-block-products--two-rows' : ''); ?>">
                        <?php foreach ($products as $product) : ?>
                            <a href="<?= $product['url']; ?>" class="sp-block-products__item">
                                <i class="sp-block-products__icon sp-block__card"><?= vector($product['icon']); ?></i>
                                <span class="sp-block-products__title"><?= $product['title']; ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>
                <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_content']); ?>
            </div>
        </div>
    </section>
<?php endif;
