<?php
getBlockCommonFields();
$topCasinos = get_field('block_top_casinos');
$gotoWebsite = get_field('go_to_website');

if (!empty($topCasinos) && is_array($topCasinos)) {
    $topCasinos = array_map(function ($topCasino) {
        if (is_array($topCasino) && !all_have_values(...$topCasino)) {
            return null;
        }
        return $topCasino;
    }, $topCasinos);

    $topCasinos = array_filter($topCasinos);
}

if (!empty($topCasinos)) {
    ob_start();
?>
    <div class="wrapper">
        <div class="sp-block-top-casino">
            <?php foreach ($topCasinos as $casino) : ?>

                <div class="sp-block-top-casino__item sp-block__card">
                    <a class="sp-block-top-casino__brand" target="_blank" href="<?= $casino['brand_url']; ?>" title="<?= $casino['brand_name']; ?>" style="background-color: <?= $casino['brand_color']; ?>;">
                        <div>
                            <img src="<?= $casino['brand_logo']; ?>" alt="<?= wp_strip_all_tags($casino['brand_name']); ?>" class="sp-block-top-casino__logo" />
                            <?php if (!empty($gotoWebsite)) : ?>
                                <div class="sp-block-top-casino__link"><?= $gotoWebsite ?> <i class="sp-block-top-casino__link__icon"><?= vector('external-link'); ?></i></div>
                            <?php endif; ?>
                        </div>
                    </a>
                    <div class="sp-block-top-casino__content">
                        <h3 class="sp-block-top-casino__name"><?= $casino['brand_name']; ?></h3>
                        <div class="sp-block-top-casino__description"><?= $casino['brand_description']; ?></div>
                    </div>
                    <div class="sp-block-top-casino__bonus" style="background-image:url(<?= $casino['bonus_image']; ?>)">
                        <div class="sp-block-top-casino__bonus__content">
                            <div class="sp-block-top-casino__bonus__tag"><?= $casino['bonus_tag']; ?></div>
                            <div class="sp-block-top-casino__bonus__title"><?= $casino['bonus_title']; ?></div>
                            <div class="sp-block-top-casino__bonus__description"><?= $casino['bonus_description']; ?></div>
                            <a class="sp-block-top-casino__bonus__button btn btn--small btn--rounded" target="_blank" href="<?= $casino['bonus_url']; ?>" title="<?= $casino['bonus_button']; ?>"><?= $casino['bonus_button']; ?></a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
