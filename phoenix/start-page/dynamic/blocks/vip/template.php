<?php
getBlockCommonFields();
$cards = get_field('block_cards');

if (!empty($cards) && is_array($cards)) {
    $cards = array_map(function ($card) {
        if (is_array($card) && !all_have_values(...$card)) {
            return null;
        }
        return $card;
    }, $cards);

    $cards = array_filter($cards);
}

if (!empty($cards)) {
    ob_start();
?>
    <div class="sp-block-vip swiper-container">
        <div class="swiper swiper--equal-height-slides" data-swiper-autoplay="3" data-swiper-centered="true">
            <div class="swiper-wrapper">
                <?php foreach ($cards as $card) : ?>
                    <div class="sp-block-vip__item sp-block__card swiper-slide">
                        <img src="<?= $card['image']; ?>" alt="<?= wp_strip_all_tags($card['title']); ?>" class="sp-block-vip__image" height="166" width="280" />
                        <h3 class="sp-block-vip__title"><?= $card['title'] ?></h3>
                        <p class="sp-block-vip__text"><?= $card['text'] ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="swiper-button-prev swiper-button-prev--small"><?= vector('left'); ?></div>
        <div class="swiper-button-next swiper-button-prev--small"><?= vector('right'); ?></div>

        <div class="swiper-pagination swiper-pagination--outer"></div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
