<?php
global $blockContent, $blockButton;
getBlockCommonFields();
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);
$type = get_field('block_type');
$fontColor = 'normal-font-color';
if (get_field('invert_text_color')) {
    $fontColor = 'invert-font-color';
}
$background = get_field('block_background');

addBackgroundInlineStyle('.sp-block--' . $block['id'], $background);

$image = get_field('block_image');
$questionsDisplay = get_field('block_questions_display');
$questions = get_field('block_questions');
$moreInfo = get_field('block_more_info');
$helpButton = get_field('block_help_button');

if (!empty($questions) && is_array($questions)) {
    $questions = array_map(function ($question) {
        if (is_array($question) && !all_have_values(...$question)) {
            return null;
        }
        return $question;
    }, $questions);

    $questions = array_filter($questions);
}

get_template_part('start-page/dynamic/parts/preview');

$spBlockClass = getClass([
    'sp-block',
    'sp-block--' . $type,
    'sp-block--' . $fontColor,
    'sp-block--' . $block['id'],
    $block['className'],
    'block-stretchable',
    (!empty($block['data']['minimize_top_padding']) ? ' sp-block--minimize-padding-top' : ''),
    (!empty($block['data']['minimize_bottom_padding']) ? ' sp-block--minimize-padding-bottom' : ''),
]);

if (at_least_one_has_value($questions, $blockContent, array_keys_have_values($blockButton, ['button_text', 'button_url']))) : ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spBlockClass; ?>">
        <?php get_template_part('campaigns/parts/background-video', null, $background); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $background); ?>
        <div class="container sp-block__container">
            <div class="wrapper sp-block-faq">
                <div class="sp-block-faq__column sp-block-faq__column--image sp-block__animation sp-block__animation--fade-in-left">
                    <?php if (!empty($image)) : ?>
                        <img src="<?= $image; ?>" class="sp-block-faq__image" alt="<?= wp_strip_all_tags($blockContent['title']); ?>" />
                    <?php endif; ?>
                </div>
                <div class="sp-block-faq__column">
                    <div class="sp-block__animation sp-block__animation--fade-in-top">
                        <?php get_template_part('start-page/dynamic/parts/block', 'content'); ?>
                    </div>

                    <div class="sp-block-faq__questions sp-block__animation sp-block__animation--fade-in-center">
                        <?php foreach ($questions as $question) : ?>
                            <?php if ($questionsDisplay == 'dropdown') : ?>
                                <div class="sp-block-faq__item">
                                    <?php get_template_part('components/dropdown', null, [
                                        'title' => $question['question'],
                                        'text' => $question['answer'],
                                        'class' => 'sp-block-faq__item--outlined',
                                    ]); ?>
                                </div>
                            <?php else : ?>
                                <div class="sp-block-faq__item sp-block-faq__item--box sp-block__card">
                                    <h3 class="sp-block-faq__question"><?= $question['question']; ?></h3>
                                    <p class="sp-block-faq__answer"><?= $question['answer']; ?></p>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>

                    <?php if (!empty($moreInfo)) : ?>
                        <div class="sp-block-faq__more-info sp-block__animation sp-block__animation--fade-in-bottom">
                            <?= $moreInfo; ?>
                        </div>
                    <?php endif; ?>

                    <div class="sp-block__animation sp-block__animation--fade-in-bottom">
                        <?php get_template_part('start-page/dynamic/parts/block', 'cta'); ?>
                    </div>

                    <?php
                    global $ZENDESK_KEY, $ada_support_widget;
                    if ((!empty($ada_support_widget) || !empty($ZENDESK_KEY)) && !empty($helpButton)) : ?>
                        <a href="<?= (!empty($ada_support_widget) ? 'javascript:window.adaEmbed.toggle();' : 'javascript:$zopim.livechat.window.show();') ?>" class="btn btn--small btn--rounded sp-block-faq__help-btn sp-block__animation sp-block__animation--fade-in-right">
                            <?= vector('support'); ?>
                            <?= $helpButton; ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

    </section>
<?php endif;
