<?php
getBlockCommonFields();
$testimonials = get_field('block_testimonials');
if (!empty($testimonials) && is_array($testimonials)) {
    $testimonials = array_map(function ($testimonial) {
        if (is_array($testimonial) && !all_have_values(...$testimonial)) {
            return null;
        }
        return $testimonial;
    }, $testimonials);

    $testimonials = array_filter($testimonials);
}

if (!empty($testimonials) && is_array($testimonials)) {
    ob_start();
?>
    <div class="sp-block-testimonial wrapper swiper-container">
        <div class="swiper swiper--equal-height-slides" data-swiper-autoplay="3" data-swiper-centered="true">
            <div class="swiper-wrapper">
                <?php foreach ($testimonials as $testimonial) : ?>
                    <div class="sp-block-testimonial__item sp-block__card swiper-slide">
                        <div class="sp-block-testimonial__details">
                            <div class="sp-block-testimonial__stars">
                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                    <i class="<?= ($i <= $testimonial['stars'] ? 'active' : '') ?>"><?= vector('star-filled'); ?></i>
                                <?php endfor; ?>
                            </div>
                            <div class="sp-block-testimonial__date"><?= formatDateMedium($testimonial['date']); ?></div>
                        </div>
                        <h3 class="sp-block-testimonial__title"><?= $testimonial['title']; ?></h3>
                        <div class="sp-block-testimonial__text"><?= $testimonial['text']; ?></div>
                        <div class="sp-block-testimonial__customer"><?= $testimonial['customer']; ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="swiper-button-prev swiper-button-prev--small"><?= vector('left'); ?></div>
        <div class="swiper-button-next swiper-button-prev--small"><?= vector('right'); ?></div>

        <div class="swiper-pagination swiper-pagination--outer"></div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
