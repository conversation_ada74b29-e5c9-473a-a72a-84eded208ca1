<?php
getBlockCommonFields();
$cards = get_field('block_cards');

if (!empty($cards) && is_array($cards)) {
    $cards = array_map(function ($card) {
        if (empty($card['image']) || empty($card['title']) || empty($card['button_url']) || empty($card['button_text'])) {
            return null;
        }
        return $card;
    }, $cards);

    $cards = array_filter($cards);
}

if (!empty($cards)) {
    ob_start();
?>
    <div class="sp-block-high-wins wrapper wrapper--tight">
        <?php foreach ($cards as $card) : ?>
            <div class="sp-block-high-wins__item sp-block__card">
                <div class="sp-block-high-wins__image">
                    <img src="<?= $card['image']; ?>" alt="<?= wp_strip_all_tags($card['title']); ?>" height="150" width="173" />
                    <?php if ($card['jackpot_amount']) : ?>
                        <div class="sp-block-high-wins__jackpots">
                            <?= $card['jackpot_amount']; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="sp-block-high-wins__content">
                    <h3 class="sp-block-high-wins__title"><?= $card['title']; ?></h3>
                    <a class="btn btn--small btn--full btn--rounded" href="<?= $card['button_url']; ?>"><?= $card['button_text']; ?></a>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
