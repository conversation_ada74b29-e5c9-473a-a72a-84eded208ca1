<?php
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);
global $content;
$content = get_field('block_content');
$image = get_field('block_image');
$layoutSettings = get_field('layout_settings');
$layoutStyle = get_field('layout_style');
$verticalAlignment = $content['vertical_alignment'];
$backgroundGroup = get_field('block_background');
$termsAndConditions = get_field('terms_and_conditions_group');
$countdownOffers = get_field('countdown_offers');
$count = (!empty($countdownOffers) ? count($countdownOffers) : 0);

$status = COUNTDOWN_STARTS_IN; // Not yet started
$today = getDateTime('today');
$now = getDateTime();
$start = getDateTime(get_field('countdown_start'));

$countdownType = get_field('countdown_type') ?? 'regular';
$countdownClockColor = get_field('countdown_clock_color');
$countdown24h = false;

// Countdown related variables
$countdownStart = $today;
$countdownEnd = $start;

$i = 0;
while (have_rows('countdown_offers')) : the_row();
    $stack[$i + 1] = get_sub_field('type');
    $i++;
endwhile;

$i = 1;
if ($now > $start) {
    $countdownStart = $start;

    while (have_rows('countdown_offers')) : the_row();
        $pauseCampaign = false;

        if (get_sub_field('type') === 'offer') {
            $offer = get_sub_field('offer');
        } else {
            $pauseCampaign = true;
        }

        $countdown = get_sub_field('countdown');

        $countdownEnd = getDateTime($countdown['offer_duration']);

        if ($now < $countdownEnd) {
            // overwrite initial content once time conditions are met
            $status = COUNTDOWN_NEW_OFFER_IN; // Has started

            if (isset($stack[$i + 1]))
                if ($stack[$i + 1] == 'pause_campaign') {
                    $status = COUNTDOWN_OFFER_ENDS_IN;
                }

            if (!$pauseCampaign) :
                $content = $offer['content'];
                $image = $offer['image'];
                $layoutStyle    = $offer['layout_style'];
                $verticalAlignment = $content['vertical_alignment'];
                $layoutSettings = $offer['layout_settings'];
            else :
                $status = COUNTDOWN_RESUMES_IN;
            endif;

            // If it's a 24 hours countdown then set the time until tomorrow
            if ($countdown['countdown_24']) {
                $countdownStart = $today;
				$countdownEnd = getDateTime('tomorrow');
				$countdown24h = true;
            }

            break;
        }
        $status = COUNTDOWN_EXPIRED; // All countdown offers done / expired
        $i++;

    endwhile;

    if ($i == $count) {
        // Last countdown offer.
        $status = COUNTDOWN_CAMPAIGN_ENDS_IN;
    }
}

// Set the expiry date field to be expired today
if ($status == COUNTDOWN_EXPIRED) {
    $today = getDateTime();
    $today->modify('-1 day');
    global $expiry_date_field;
    $expiry_date_field = $today->format('Ymd');
}

get_template_part('start-page/dynamic/parts/preview');

if (!empty($content['campaign_header']) && !empty($content['button_url']) && !empty($content['button_text'])) : ?>
    <?php
    $spMainClass = getClass([
        'sp-block sp-block-main background content-stretchable block-stretchable',
        'sp-block-main--' . $block['id'],
        'sp-block-main--vh-mobile-' . get_field('main_block_mobile_height'),
        'sp-block-main--vh-desktop-' . get_field('main_block_desktop_height'),
        $block['className'],
    ]);

    addBackgroundInlineStyle('.sp-block-main--' . $block['id'], $backgroundGroup);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spMainClass; ?>">

        <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

        <?php
        $contentContainerClass = getClass([
            'content-margin campaign__content container wrapper sp-block-main__content',
            [
                'condition' => ($layoutStyle === 'centered'),
                'name' => 'campaign__content--centered',
                'else-name' => 'wrapper--md-80',
            ],
            [
                'condition' => ($layoutStyle === 'centered' &&
                    empty($layoutSettings['full_width'])),
                'name' => 'wrapper--tight'
            ],
            [
                'condition' => !empty($content['invert_text_color']),
                'name' => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ],
            [
                'condition' => !empty($layoutSettings['full_width']),
                'name' => 'wrapper--wider'
            ]
        ]);
        ?>
        <div class="<?= $contentContainerClass; ?>">
            <?php
            // Backwards compatibility with 'grid-left-and-right' class fallback
            $layoutStyleClass = 'grid-' . ($layoutStyle ?? 'left-and-right');

            $gridLeftAndRightContainerClass = getClass([
                $layoutStyleClass,
                [
                    'condition' => !empty($content['image_above_heading']),
                    'name' => $layoutStyleClass . '--has-image-above-heading',
                ],
                [
                    'condition' => !empty($layoutSettings['columns_size']),
                    'name' => $layoutStyleClass . '--' . $layoutSettings['columns_size'],
                ]
            ]);
            ?>
            <div class="<?= $gridLeftAndRightContainerClass; ?>">
                <!-- content -->
                <?php
                $gridLeftColumnClass = getClass([
                    $layoutStyleClass . '__left',
                    'sp-block__animation',
                    'sp-block__animation--fade-in-left',
                    [
                        'condition' => !empty($verticalAlignment),
                        'name' => $layoutStyleClass . '__left--' . $verticalAlignment,
                    ]
                ]);
                ?>
                <div class="<?= $gridLeftColumnClass; ?>">
                    <?php if (!empty($content['tags'])) : ?>
                        <div class="tags">
                            <?php foreach ($content['tags'] as $tag) : ?>
                                <span class="tag"><?= $tag['tag'] ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    <?php get_template_part('campaigns/parts/image-above-title'); ?>
                    <?php get_template_part('campaigns/parts/title'); ?>
                    <?php get_template_part('campaigns/parts/subtitle'); ?>
                    <?php get_template_part('campaigns/parts/description'); ?>


                    <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'above_cta'])); ?>

                    <?php
                    get_template_part('campaigns/parts/countdown-sticky', null, [
                        'status' => $status,
                        'time_end' => $countdownEnd,
                        'time_start' => $countdownStart,
                        '24h' => $countdown24h,
                        'type' => $countdownType,
                        'color' => $countdownClockColor,
                    ]);
                    ?>

                    <?php if (!$pauseCampaign) : ?>
                        <?php ob_start(); ?>
                        <?php
                        $buttonClass = getClass([
                            'btn',
                            [
                                'condition' => !empty($content['button_color']),
                                'name' => 'btn--' . $content['button_color'],
                            ],
                            [
                                'condition' => !empty($content['button_size']),
                                'name' => 'btn--' . $content['button_size'],
                            ],
                            [
                                'condition' => !empty($content['button_animation']),
                                'name' => 'btn--animation-' . $content['button_animation'],
                            ],
                            [
                                'condition' => !empty($content['button_tracking']),
                                'name' => 'js-' . $content['button_tracking'],
                                'else-name' => 'js-button-claim',
                            ]
                        ]);
                        ?>
                        <a href="<?= $content['button_url']; ?>" class="<?= $buttonClass; ?>">
                            <?= $content['button_text']; ?>
                        </a>
                        <?php
                        $stickyButtonElement = ob_get_clean();
                        get_template_part('campaigns/parts/cta-button-sticky', null, [
                            'button' => $stickyButtonElement,
                            'sticky' => empty($content['not_sticky_mobile_button']),
                        ]);
                        ?>
                    <?php endif; ?>

                    <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'below_cta'])); ?>

                </div>

                <div class="<?= $layoutStyleClass; ?>__right sp-block__animation sp-block__animation--fade-in-right">
                    <?php
                    if (!empty($image)) {
                        $imageArgs = array_merge(
                            $image,
                            [
                                'type' => ($layoutStyle === 'centered' ? 'small' : ''),
                                'alt' => $content['campaign_header'],
                                'link' => $content['button_url']
                            ]
                        );
                        get_template_part('campaigns/parts/offer-media', null, $imageArgs);
                    }
                    ?>
                </div>
            </div>

            <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'below_content'])); ?>
        </div>
    </section>
<?php
endif;
