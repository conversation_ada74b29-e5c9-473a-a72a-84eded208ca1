<?php
getBlockCommonFields();
$cards = get_field('block_cards');

if (!empty($cards) && is_array($cards)) {
    $cards = array_map(function ($card) {
        if (is_array($card) && !array_keys_have_values($card, ['title', 'text', 'url'])) {
            return null;
        }
        return $card;
    }, $cards);

    $cards = array_filter($cards);
}

if (!empty($cards)) {
    ob_start();
?>
    <div class="sp-block-products-grid wrapper">
        <?php foreach ($cards as $card) : ?>
            <a href="<?= $card['url'] ?>" class="sp-block-products-grid__item sp-block__card">
                <?php if ($card['display'] == 'icon') : ?>
                    <i class="sp-block-products-grid__icon"><?= vector($card['icon']); ?></i>
                <?php endif; ?>
                <?php if ($card['display'] == 'image') : ?>
                    <img src="<?= $card['image']; ?>" alt="<?= wp_strip_all_tags($card['title']); ?>" class="sp-block-products-grid__image" />
                <?php endif; ?>
                <h3 class="sp-block-products-grid__title"><?= $card['title'] ?></h3>
                <p class="sp-block-products-grid__text"><?= $card['text'] ?></p>
            </a>
        <?php endforeach; ?>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('start-page/dynamic/parts/block', null, $block);
