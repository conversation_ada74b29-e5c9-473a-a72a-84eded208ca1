<?php
$gameTypes = get_field('game_types');

if (!empty($gameTypes['game_types']) && is_array($gameTypes['game_types'])) {
    $array = $gameTypes['game_types'];
    $array = array_map(function ($gameType) {
        if (empty($gameType['title']) || empty($gameType['image']['url'])) {
            return null;
        }
        return $gameType;
    }, $array);

    $gameTypes['game_types'] = array_filter($array);
}

get_template_part('start-page/dynamic/parts/preview');

if (is_array($gameTypes['game_types']) && !empty($gameTypes['game_types'])) {
?>
    <section class="sp-block block-stretchable pnp-block pnp-block-products">
        <div class="wrapper pnp-block__container">
            <h3 class="sp-block__title pnp-block__title"><?= $gameTypes['title'] ?></h3>
            <div class="pnp-block-products__grid">
                <?php foreach ($gameTypes['game_types'] as $gameType): ?>
                    <a <?= ($gameType['url']) ? 'href="' . $gameType['url'] . '"' : ''; ?> title="<?= $gameType['title'] ?>" class="pnp-block-products__item">
                        <h4 class="pnp-block-products__title"><?= $gameType['title'] ?></h4>
                        <img class="pnp-block-products__img" src="<?= $gameType['image']['url'] ?>" alt="<?= wp_strip_all_tags($gameType['title']); ?>">
                        <?= $gameType['url'] ? '</a>' : ''; ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
<?php
}
