<?php
$offers = get_field('offers');

if (!empty($offers['offers']) && is_array($offers['offers'])) {
    $array = $offers['offers'];
    $array = array_map(function ($offer) {
        if (empty($offer['offer_image']) || empty($offer['offer_title'])) {
            return null;
        }
        return $offer;
    }, $array);

    $offers['offers'] = array_filter($array);
}

get_template_part('start-page/dynamic/parts/preview');

if (is_array($offers['offers']) && !empty($offers['offers'])) {
?>
    <section class="sp-block block-stretchable pnp-block pnp-block-offers">
        <div class="wrapper pnp-block__container">
            <h3 class="sp-block__title pnp-block__title"><?= $offers['promotions_section_title'] ?></h3>
            <div class="pnp-block-offers__grid swiper-container">
                <div class="swiper swiper--equal-height-slides swiper--force-auto-height" data-swiper-autoplay="3" data-swiper-centered="true">
                    <div class="swiper-wrapper">
                        <?php foreach ($offers['offers'] as $offer): ?>
                            <div class="pnp-block-offers__item swiper-slide">
                                <div class="pnp-block-offers__container">
                                    <a href="<?= $offer['offer_cta_url'] ?>" class="js-button-register" title="<?= $offer['offer_title'] ?>">
                                        <img class="pnp-block-offers__img" src="<?= $offer['offer_image'] ?>" alt="<?= wp_strip_all_tags($offer['offer_title']); ?>">
                                    </a>
                                    <h3 class="pnp-block-offers__title"><?= $offer['offer_title'] ?></h3>
                                    <p class="pnp-block-offers__description"><?= $offer['offer_description'] ?></p>
                                    <a class="btn btn--primary btn--rounded btn--small pnp-block-offers__cta js-button-register" href="<?= $offer['offer_cta_url'] ?>" title="<?= $offer['offer_title'] ?>">
                                        <?= $offer['offer_cta'] ?>
                                    </a>
                                    <div class="pnp-block-offers__terms">
                                        <?= $offer['offer_terms'] ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="swiper-button-prev swiper-button-prev--small"><?= vector('left'); ?></div>
                <div class="swiper-button-next swiper-button-prev--small"><?= vector('right'); ?></div>

                <div class="swiper-pagination swiper-pagination--outer"></div>
            </div>
        </div>
    </section>

<?php
}
