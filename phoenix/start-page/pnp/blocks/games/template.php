<?php
$games = get_field('games');
$showAllGames = $games['show_all_games_link'];

if (!empty($games['games']) && is_array($games['games'])) {
    if (!empty($games['enable_casino_games'])) {
        $games['games'] = CasinoGame::getGames($games['games']);
    }
    $array = $games['games'];
    $array = array_map(function ($game) {
        if (empty($game['game_title']) || empty($game['game_image'])) {
            return null;
        }
        return $game;
    }, $array);

    $games['games'] = array_filter($array);
}

get_template_part('start-page/dynamic/parts/preview');

if (is_array($games['games']) && !empty($games['games']) && is_array($showAllGames) && all_have_values(...$showAllGames)) {
?>
    <section class="sp-block block-stretchable pnp-block pnp-block-games">
        <div class="wrapper pnp-block__container">
            <h3 class="sp-block__title pnp-block__title"><?= $games['games_section_title'] ?></h3>
            <div class="pnp-block-games__grid">
                <?php
                foreach ($games['games'] as $game) {
                    get_template_part('components/game-card', null, [
                        'size'                => '1x1',
                        'size-lg'            => '1.5x1.5',
                        'url'               => $game['game_url'],
                        'name'              => $game['game_title'],
                        'bg_image'          => $game['game_image'],
                        'cta_text'          => (!empty($games['custom_cta_text']) ? $games['custom_cta_text'] : get_field_tweaked('play_now', 'option'))
                    ]);
                }
                ?>
            </div>
            <a class="btn btn--rounded btn--primary" href="<?= $showAllGames['link_address'] ?>"
                title="<?= wp_strip_all_tags($showAllGames['link_text']); ?>">
                <?= $showAllGames['link_text'] ?>
            </a>
        </div>
    </section>
<?php
}
