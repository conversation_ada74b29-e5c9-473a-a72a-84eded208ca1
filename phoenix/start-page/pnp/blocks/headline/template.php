<?php
global $loginDepositMethodsOverAPI;

$block = array_merge([
    'id'        => '',
    'className' => '',
    'anchor'    => '',
], $block);

$pnpPage              = get_fields();
$casinoInfo           = get_field('casino_info', 'options');
$paymentPlaceholder   = get_field_tweaked('enter_amount', 'option');
$content              = $pnpPage['offer'];
$hideInMobile         = $content['hide_title_and_body'];
$paymentFormLayout    = !empty($pnpPage['payment_form_layout']) ? $pnpPage['payment_form_layout'] : 'buttons-layout'; // Fallback for first version (buttons layout)
$paymentMinimumAmount = $pnpPage['minimum_amount'];
$paymentMaximumAmount = !empty($pnpPage['maximum_amount']) ? $pnpPage['maximum_amount'] : false;
$paymentAmount        = ((intval($pnpPage['prefill_with_the_minimum_amount']) ? (int)$paymentMinimumAmount . ' ' . CURRENT_CURRENCY_SYMBOL : ''));
$paymentCTAColor      = !empty($pnpPage['button_color']) ? $pnpPage['button_color'] : 'primary';

// Backwards compatibility
// Offer Media
if(!empty($content['media_type']) && empty($content['image_type'])) {
    $content['image_type'] = $content['media_type'];
}
if(!empty($content['media_alignment']) && empty($content['alignment'])) {
    $content['alignment'] = $content['media_alignment'];
}
if(!empty($content['image']['url']) && empty($content['image'])) {
    $content['image'] = $content['image']['url'];
}
if(!empty($content['lottie_file']['url']) && empty($content['lottie'])) {
    $content['lottie'] = $content['lottie_file']['url'];
}

get_template_part('start-page/dynamic/parts/preview');

if (!empty($content['title'])) {

    // Sticky PNP Form on Desktop
    $paymentFormDesktopClass = getClass([
        'payment-form payment-form--fixed payment-form--desktop',
        [
            'condition' => ($paymentFormLayout === "toggle-layout"),
            'name' => 'payment-form--desktop-toggle',
        ]
    ]);

    ob_start(); ?>
    <div class="<?= $paymentFormDesktopClass ?>">
        <?php
            if($paymentFormLayout === "toggle-layout") {
                include(locate_template('start-page/pnp/blocks/headline/layouts/payment-form-layout-toggle.php', false, false));
            }
            else {
                include(locate_template('start-page/pnp/blocks/headline/layouts/payment-form-layout-buttons.php', false, false));
            }
        ?>
        <?php if (!empty($pnpPage['payment_tc'])) : ?>
            <p class="payment-form--fixed__terms-and-conditions"><?= $pnpPage['payment_tc']; ?></p>
        <?php endif; ?>
    </div>
    <?php
    global $stickyButtonFooterElements;
    $stickyButtonFooterElements .= ob_get_clean();

    // Sticky PNP Form on Mobile
    $paymentFormMobileClass = getClass([
        'payment-form payment-form--fixed payment-form--mobile',
        [
            'condition' => ($paymentFormLayout === "toggle-layout"),
            'name' => 'payment-form--mobile-toggle',
        ]
    ]);

    ob_start(); ?>
    <div class="<?= $paymentFormMobileClass ?>">
        <?php
            if($paymentFormLayout === "toggle-layout") {
                include(locate_template('start-page/pnp/blocks/headline/layouts/payment-form-layout-toggle.php', false, false));
            }
            else {
                include(locate_template('start-page/pnp/blocks/headline/layouts/payment-form-layout-buttons.php', false, false));
            }
        ?>
    </div>
    <?php
    global $bottomNotificationElements;
    $bottomNotificationElements .= ob_get_clean();
    ?>

    <?php
    $blockClass = getClass([
        'sp-block pnp-block-headline background content-stretchable block-stretchable',
        'pnp-block-headline--' . $block['id'],
        $block['className'],
    ]);

    addBackgroundInlineStyle('.pnp-block-headline--' . $block['id'], $content);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $blockClass; ?>">

        <?php
        get_template_part('campaigns/parts/background-lottie', null, $content);
        get_template_part('campaigns/parts/background-video', null, $content);

        include(locate_template('start-page/pnp/blocks/headline/casino-info-bar.php', false, false));

        $containerClass = getClass([
            'container pnp-block-headline__layout',
            [
                'condition' => !empty($content['layout_style']),
                'name' => 'pnp-block-headline__layout--' . $content['layout_style'],
            ],
            [
                'condition' => !empty($content['image_above_heading']),
                'name' => 'pnp-block-headline--has-image-above-heading',
            ]
        ]);
        ?>
        <div class="<?= $containerClass ?>">
            <div class="pnp-block-headline__left">
                <div class="pnp-block-headline__left-container">
                    <?php if (!empty($content['tags'])) : ?>
                        <div class="tags<?= ($hideInMobile) ? ' hide-in-mobile' : '' ?>">
                            <?php foreach ($content['tags'] as $tag) : ?>
                                <span class="tag"><?= $tag['label'] ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($content['image_above_heading'])) : ?>
                        <div class="campaign__image-above-heading">
                            <img src="<?= $content['image_above_heading']; ?>" alt="<?= wp_strip_all_tags($content['title']); ?>">
                        </div>
                    <?php endif; ?>
                    <h1 class="main-title"><?= $content['title']; ?></h1>
                    <?php if (trim((string) $content['subtitle']) != '') : ?>
                        <h2 class="sub-title<?= ($hideInMobile) ? ' hide-in-mobile' : '' ?>"><?= $content['subtitle'] ?></h2>
                    <?php endif; ?>

                    <?php if (trim((string) $content['offer_description']) != '') : ?>
                        <div class="offer-description<?= ($hideInMobile) ? ' hide-in-mobile' : '' ?>">
                            <?php get_template_part('components/read-more', null, $content['offer_description']); ?>
                        </div>
                    <?php endif; ?>

                    <?php
                        $paymentFormNormalClass = getClass([
                            'payment-form payment-form--normal',
                            [
                                'condition' => ($paymentFormLayout === "toggle-layout"),
                                'name' => 'payment-form--normal-toggle',
                            ]
                        ]);
                    ?>
                    <div class="<?= $paymentFormNormalClass ?>">
                        <?php
                            if($paymentFormLayout === "toggle-layout") {
                                include(locate_template('start-page/pnp/blocks/headline/layouts/payment-form-layout-toggle.php', false, false));
                            }
                            else {
                                include(locate_template('start-page/pnp/blocks/headline/layouts/payment-form-layout-buttons.php', false, false));
                            }
                        ?>
                    </div>
                    <?php if(!empty($content['cta']['cta_button_url']) && !empty($content['cta']['cta_button_text'])): ?>
                        <a href="<?= $content['cta']['cta_button_url'] ?>" title="<?= wp_strip_all_tags($content['title']); ?>" class="payment-form__resume-playing">
                            <?= $content['cta']['cta_button_text'] ?>
                        </a>
                    <?php endif; ?>

                    <?php if (!empty($content['terms_and_conditions'])) : ?>
                        <div class="terms-and-conditions">
                            <?= $content['terms_and_conditions']; ?>

                            <?php if ($content['terms_and_conditions_more']) : ?>
                                <a href="#" class="terms-and-conditions__read-more js-terms-and-conditions-read-more"><?= $content['read_more']; ?></a>
                                <span class="terms-and-conditions__more js-terms-and-conditions-more"><?= $content['terms_and_conditions_more']; ?></span>
                                <a href="#" class="terms-and-conditions__read-less js-terms-and-conditions-read-less"><?= $content['read_less']; ?></a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php get_template_part('components/terms-and-conditions'); ?>

                    <?php
                    $paymentOptionsLogos = [];
                    if (!empty($loginDepositMethodsOverAPI['loginPaymentMethodDataList'])) {
                        foreach ($loginDepositMethodsOverAPI['loginPaymentMethodDataList'] as $paymentMethod) {
                            if (!empty($paymentMethod['methodName']))
                                $paymentOptionsLogos[] = $paymentMethod['methodName'];
                        }
                    } else {
                        $paymentOptionsLogos = $pnpPage['payment_options'];
                    }
                    if (!empty($paymentOptionsLogos)) : ?>
                        <div class="payment-logo-bar">
                            <?php
                            foreach ($paymentOptionsLogos as $logoName) : ?>
                                <div class="payment-logo <?= $logoName; ?>">
                                    <?= vector($logoName, 'logos/payment'); ?>
                                </div>
                            <?php
                            endforeach;
                            ?>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
            <div class="pnp-block-headline__right">
                <?php
                if(!empty($content['image'])) {
                    $imageArgs = array_merge(
                        $content,
                        [
                            'alt' => $content['title']
                        ]
                    );
                    get_template_part('campaigns/parts/offer-media', null, $imageArgs);
                }
                ?>
            </div>
        </div>
    </section>
<?php
}
